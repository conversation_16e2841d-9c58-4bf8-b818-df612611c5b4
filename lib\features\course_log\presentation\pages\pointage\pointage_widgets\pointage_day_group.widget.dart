import 'package:kairos/core/utils/date_utils.dart' as du;
import 'package:flutter/material.dart';
import 'package:kairos/features/course_log/domain/entities/course_log_entity.dart';
import 'pointage_item.widget.dart';

/// Widget that groups pointage entries by day - matches CourseLogDayGroup design
class PointageDayGroup extends StatelessWidget {
  final List<CourseLogEntity> dayLogs;

  const PointageDayGroup({
    super.key,
    required this.dayLogs,
  });

  @override
  Widget build(BuildContext context) {
    if (dayLogs.isEmpty) return const SizedBox.shrink();

    final date = dayLogs.first.dateCours;
    final weekdayAbbr = _getWeekdayAbbreviation(date);
    final dateStr = _formatDateForHeader(date);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 7.0, horizontal: 0.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Weekday and date column (left) - matching course log design
          Padding(
            padding: const EdgeInsets.only(right: 0.0, top: 4.0, left: 2.0),
            child: SizedBox(
              width: 38,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    weekdayAbbr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    dateStr,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Pointage items column (right)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: dayLogs.map((pointage) =>
                Padding(
                  padding: const EdgeInsets.only(bottom: 1.0),
                  child: PointageItem(pointage: pointage),
                )
              ).toList(),
            ),
          ),
        ],
      ),
    );
  }

  String _getWeekdayAbbreviation(String dateString) {
    try {
      final date = du.parseDate(dateString);
      switch (date.weekday) {
        case 1:
          return 'LUN';
        case 2:
          return 'MAR';
        case 3:
          return 'MER';
        case 4:
          return 'JEU';
        case 5:
          return 'VEN';
        case 6:
          return 'SAM';
        case 7:
          return 'DIM';
        default:
          return '';
      }
    } catch (e) {
      return '';
    }
  }

  String _formatDateForHeader(String dateString) {
    try {
      final date = du.parseDate(dateString);
      return "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}";
    } catch (e) {
      return '';
    }
  }
}
