import 'package:flutter/material.dart';
import 'package:kairos/features/course_log/domain/entities/course_log_entity.dart';

/// Widget that displays a single pointage entry - matches CourseLogItem design
class PointageItem extends StatelessWidget {
  final CourseLogEntity pointage;

  const PointageItem({
    super.key,
    required this.pointage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 7.0, vertical: 4.0),
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(7.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row( // Row 1: Time, Professor, Duration, Status
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Text(
                        '${pointage.heureDebutPrevu} - ${pointage.heureFinPrevu} |',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Colors.black.withValues(alpha: 0.8),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(width: 4),
                    const Icon(Icons.access_time_filled_rounded, size: 14, color: Colors.black),
                      const SizedBox(width: 2),
                    Text(
                      pointage.dureeSaisi,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.black.withValues(alpha: 0.8),
                      ),
                    ),
                    ],
                  ),
                ),
                Row( // Row for clock icon, duration, and status
                  children: [
                    const SizedBox(width: 8),
                    Text('Statut :', style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),),
                    const SizedBox(width: 2),
                    Text(
                      _getStatusText(),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: _getStatusColor(),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text( 
              pointage.cours,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 4),
            Row( 
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                 Expanded(
                  child: 
                  Text(
                  '${pointage.classe} | ${pointage.semestre}',
                  style: TextStyle(
                    fontSize: 8,
                    fontWeight: FontWeight.w400,
                    color: Colors.black.withValues(alpha: 0.5),
                  ),
                ),
                ),
                Text(
                    'Enregistré le ${_formatDate(pointage.dateCours)}',
                    style: TextStyle(
                      fontSize: 8,
                      fontWeight: FontWeight.w400,
                      color: Colors.black.withValues(alpha: 0.5),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
               
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getStatusText() {
    // Mock status logic - alternating between "Validé" and "Décompté" based on course name
    if (pointage.cours.hashCode % 2 == 0) {
      return "Validé";
    } else {
      return "Décompté";
    }
  }

  Color _getStatusColor() {
    // Color coding for status
    if (pointage.cours.hashCode % 2 == 0) {
      return Colors.green; // Validé in green
    } else {
      return Colors.orange; // Décompté in orange
    }
  }

  // Helper method to format date as dd/mm (matching course log pattern)
  String _formatDate(String dateString) {
    try {
      final dateParts = dateString.split('-');
      if (dateParts.length >= 3) {
        return '${dateParts[2]}/${dateParts[1]}';
      }
      return dateString;
    } catch (e) {
      return dateString;
    }
  }
}
